.offer-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.offer-modal {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.offer-modal .offer-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 24px;
}

.offer-modal .offer-modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--secondary-color);
}

.offer-modal .offer-modal-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.offer-modal .offer-modal-close:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.offer-modal .offer-modal-close:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.offer-modal .offer-form {
  padding: 0 24px 24px;
}

.offer-modal .content-info {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.offer-modal .content-info h4 {
  margin: 0 0 8px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--secondary-color);
}

.offer-modal .content-info p {
  margin: 4px 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.offer-modal .listed-price {
  font-weight: 600;
  color: #059669 !important;
}

.offer-modal .form-group {
  margin-bottom: 20px;
}

.offer-modal .form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.offer-modal .form-group input,
.offer-modal .form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  box-sizing: border-box;
}

.offer-modal .form-group input:focus,
.offer-modal .form-group textarea:focus {
  outline: none;
  border-color: var(--dark-gray);
 
}

.offer-modal .form-group input:disabled,
.offer-modal .form-group textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.offer-modal .char-count {
  display: block;
  text-align: right;
  margin-top: 4px;
  font-size: 0.75rem;
  color: #6b7280;
}

.offer-modal .offer-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.offer-modal .offer-modal-actions button {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  
}

.offer-modal .btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.offer-modal .btn-secondary:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.offer-modal .btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Success State */
.offer-modal .offer-success-content {
  padding: 0 24px 24px;
  text-align: center;
}

.offer-modal .success-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.offer-modal .success-icon img {
  width: 64px;
  height: 64px;
  animation: zoomInOut 2s ease-in-out infinite;
}

.offer-modal .offer-success-content h3 {
  margin: 0 0 12px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #059669;
}

.offer-modal .offer-success-content > p {
  margin: 0 0 24px 0;
  color: #6b7280;
  font-size: 1rem;
}

.offer-modal .offer-details {
  background-color: #f0fdf4;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
}

.offer-modal .offer-details p {
  margin: 8px 0;
  font-size: 0.875rem;
  color: #374151;
}

.offer-modal .offer-details strong {
  color: #059669;
}

/* Responsive */
@media (max-width: 640px) {
  .offer-modal-overlay {
    padding: 10px;
  }

  .offer-modal {
    width: 95%;
    max-height: 95vh;
  }

  .offer-modal .offer-modal-header {
    padding: 20px 20px 0;
  }

  .offer-modal .offer-form {
    padding: 0 20px 20px;
  }

  .offer-modal .offer-modal-actions {
    flex-direction: column;
  }

  .offer-modal .offer-modal-actions button {
    width: 100%;
  }
}

/* Success Icon Animation */
@keyframes zoomInOut {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
