/* Universal Modal Scroll Containment Fix */

/* Apply scroll prevention to all modal overlays */
.modal-overlay,
.bid-modal-overlay,
.offer-modal-overlay,
.preview-modal-overlay,
.request-modal-overlay,
.confirmation-modal-overlay,
.role-modal-overlay,
.seller-offer-management .offer-modal-overlay,
.SellerOffers .modal-overlay,
.OfferDetails__modal-overlay,
.UserDetailModal__overlay,
.CMSEditorModal__overlay,
.content-submission-modal-overlay,
.create-request-modal-overlay,
.custom-content-modal-overlay,
.AdminReviewManagement .modal-overlay,
.review-modal-overlay,
.AddContentModal__overlay,
.request-custom-training-modal-overlay {
  /* Prevent scroll propagation to background */
  overscroll-behavior: contain;
  /* Allow vertical scrolling with mouse wheel while preventing horizontal scroll */
  touch-action: pan-y;
}

/* Allow touch actions within modal content areas */
/* .modal-content, */
.bid-modal,
.offer-modal,
.preview-modal,
.request-modal,
.confirmation-modal,
.role-modal,
.auction-bid-modal,
.offer-acceptance-modal,
.seller-offer-management .offer-review-modal,
.SellerOffers .response-modal,
.OfferDetails__response-modal,
.UserDetailModal__container,
.CMSEditorModal__container,
.bid-detail-modal,
.FAQEditorModal,
.FAQDetailModal,
.content-submission-modal,
.create-request-modal,
.custom-content-modal,
.AdminReviewManagement .modal,
.review-modal,
.AddContentModal__container,
.role-modal-overlay .role-modal,
.request-modal-overlay .request-modal {
  /* Enable proper scroll containment */
  overscroll-behavior: contain;
  /* Allow touch actions within modal content */
  touch-action: auto;
  /* Enable smooth scrolling */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  /* Ensure mouse wheel events work properly */
  overflow-y: auto;
}

/* Enhanced scroll containment for nested scrollable areas */

.bid-modal *,
.offer-modal *,
.preview-modal *,
.request-modal *,
.confirmation-modal *,
.role-modal *,
.auction-bid-modal *,
.offer-acceptance-modal *,
.seller-offer-management .offer-review-modal *,
.SellerOffers .response-modal *,
.OfferDetails__response-modal *,
.UserDetailModal__container *,
.CMSEditorModal__container *,
.bid-detail-modal *,
.FAQEditorModal *,
.FAQDetailModal *,
.content-submission-modal *,
.create-request-modal *,
.custom-content-modal *,
.AdminReviewManagement .modal *,
.review-modal *,
.AddContentModal__container *,
.role-modal-overlay .role-modal *,
.request-modal-overlay .request-modal * {
  /* Ensure all child elements respect scroll containment */
  overscroll-behavior: contain;
}

/* Body class for when any modal is open */
body.modal-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  top: 0 !important;
}

/* Prevent background scroll on mobile devices */
@media (max-width: 768px) {
  .modal-overlay,
  .bid-modal-overlay,
  .offer-modal-overlay,
  .preview-modal-overlay,
  .request-modal-overlay,
  .confirmation-modal-overlay,
  .role-modal-overlay,
  .seller-offer-management .offer-modal-overlay,
  .SellerOffers .modal-overlay,
  .OfferDetails__modal-overlay,
  .UserDetailModal__overlay,
  .CMSEditorModal__overlay,
  .content-submission-modal-overlay,
  .create-request-modal-overlay,
  .custom-content-modal-overlay,
  .AdminReviewManagement .modal-overlay,
  .review-modal-overlay,
  .AddContentModal__overlay,
  .request-custom-training-modal-overlay {
    /* More aggressive scroll prevention on mobile */
    position: fixed;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure modal content can still scroll on mobile */

  .bid-modal,
  .offer-modal,
  .preview-modal,
  .request-modal,
  .confirmation-modal,
  .role-modal,
  .auction-bid-modal,
  .offer-acceptance-modal,
  .seller-offer-management .offer-review-modal,
  .SellerOffers .response-modal,
  .OfferDetails__response-modal,
  .UserDetailModal__container,
  .CMSEditorModal__container,
  .bid-detail-modal,
  .FAQEditorModal,
  .FAQDetailModal,
  .content-submission-modal,
  .create-request-modal,
  .custom-content-modal,
  .AdminReviewManagement .modal,
  .review-modal,
  .AddContentModal__container,
  .role-modal-overlay .role-modal,
  .request-modal-overlay .request-modal {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    max-height: 90vh;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .modal-overlay,
  .bid-modal-overlay,
  .offer-modal-overlay,
  .preview-modal-overlay,
  .request-modal-overlay,
  .confirmation-modal-overlay,
  .role-modal-overlay,
  .seller-offer-management .offer-modal-overlay,
  .SellerOffers .modal-overlay,
  .OfferDetails__modal-overlay,
  .UserDetailModal__overlay,
  .CMSEditorModal__overlay,
  .content-submission-modal-overlay,
  .create-request-modal-overlay,
  .custom-content-modal-overlay,
  .AdminReviewManagement .modal-overlay,
  .review-modal-overlay,
  .AddContentModal__overlay,
  .request-custom-training-modal-overlay {
    /* iOS Safari scroll fix */
    position: fixed;
    -webkit-overflow-scrolling: touch;
    overflow: hidden;
  }
}

/* Android Chrome specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modal-overlay,
  .bid-modal-overlay,
  .offer-modal-overlay,
  .preview-modal-overlay,
  .request-modal-overlay,
  .confirmation-modal-overlay,
  .role-modal-overlay,
  .seller-offer-management .offer-modal-overlay,
  .SellerOffers .modal-overlay,
  .OfferDetails__modal-overlay,
  .UserDetailModal__overlay,
  .CMSEditorModal__overlay,
  .content-submission-modal-overlay,
  .create-request-modal-overlay,
  .custom-content-modal-overlay,
  .AdminReviewManagement .modal-overlay,
  .review-modal-overlay,
  .AddContentModal__overlay,
  .request-custom-training-modal-overlay {
    /* Android Chrome scroll fix - allow vertical scrolling */
    touch-action: pan-y;
    overscroll-behavior: contain;
  }
}

/* Additional mouse wheel support for all modal content areas */
.bid-modal,
.offer-modal,
.preview-modal,
.request-modal,
.confirmation-modal,
.role-modal,
.auction-bid-modal,
.offer-acceptance-modal,
.seller-offer-management .offer-review-modal,
.SellerOffers .response-modal,
.OfferDetails__response-modal,
.UserDetailModal__container,
.CMSEditorModal__container,
.bid-detail-modal,
.FAQEditorModal,
.FAQDetailModal,
.content-submission-modal,
.create-request-modal,
.custom-content-modal,
.AdminReviewManagement .modal,
.review-modal,
.AddContentModal__container,
.role-modal-overlay .role-modal,
.request-modal-overlay .request-modal {
  /* Ensure mouse wheel events are properly handled */
  pointer-events: auto;
  /* Enable scrolling */
  overflow-y: auto;
  overflow-x: hidden;
}
