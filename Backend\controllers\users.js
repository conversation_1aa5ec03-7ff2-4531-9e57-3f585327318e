const ErrorResponse = require("../utils/errorResponse");
const User = require("../models/User");
const { validationResult } = require("express-validator");

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
exports.getUsers = async (req, res, next) => {
  try {
    const users = await User.find();

    res.status(200).json({
      success: true,
      count: users.length,
      data: users,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
exports.getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create user
// @route   POST /api/users
// @access  Private/Admin
exports.createUser = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const user = await User.create(req.body);

    res.status(201).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
exports.updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndDelete(req.params.id);

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
exports.updateProfile = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Fields to update
    const fieldsToUpdate = {
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      email: req.body.email,
      mobile: req.body.mobile,
      bio: req.body.bio,
    };

    // If user has seller access, update seller info (use effective role)
    const effectiveRole =
      req.user.role === "admin" ? req.user.role : req.user.activeRole;
    if (
      (effectiveRole === "seller" || req.user.role === "admin") &&
      req.body.sellerInfo
    ) {
      fieldsToUpdate.sellerInfo = req.body.sellerInfo;
    }

    const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller profile
// @route   GET /api/users/sellers/:id
// @access  Public
exports.getSellerProfile = async (req, res, next) => {
  try {
    const user = await User.findOne({
      _id: req.params.id,
      role: "seller",
    }).populate("content");

    if (!user) {
      return next(
        new ErrorResponse(`Seller not found with id of ${req.params.id}`, 404)
      );
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Verify seller
// @route   PUT /api/users/verify-seller/:id
// @access  Private/Admin
exports.verifySeller = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(
        new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
      );
    }

    if (user.role !== "seller") {
      return next(new ErrorResponse(`User is not a seller`, 400));
    }

    user.isVerified = true;
    await user.save();

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update seller onboarding info
// @route   PUT /api/users/seller/onboarding
// @access  Private/Seller
exports.updateSellerOnboarding = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    // Check if user has seller access (use effective role like the auth middleware)
    const effectiveRole =
      req.user.role === "admin" ? req.user.role : req.user.activeRole;
    if (effectiveRole !== "seller" && req.user.role !== "admin") {
      return next(
        new ErrorResponse("Only sellers can update onboarding info", 403)
      );
    }

    const {
      description,
      experiences,
      minTrainingCost,
      socialLinks,
      sports,
      expertise,
      certifications,
    } = req.body;

    // Build sellerInfo update object
    const sellerInfoUpdate = {
      ...req.user.sellerInfo,
      ...(description !== undefined && { description }),
      ...(experiences !== undefined && { experiences }),
      ...(minTrainingCost !== undefined && { minTrainingCost }),
      ...(socialLinks !== undefined && { socialLinks }),
      ...(sports !== undefined && { sports }),
      ...(expertise !== undefined && { expertise }),
      ...(certifications !== undefined && { certifications }),
    };

    const user = await User.findByIdAndUpdate(
      req.user.id,
      { sellerInfo: sellerInfoUpdate },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: user.sellerInfo,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller onboarding status
// @route   GET /api/users/seller/onboarding
// @access  Private/Seller
exports.getSellerOnboardingStatus = async (req, res, next) => {
  try {
    // Check if user has seller access (use effective role like the auth middleware)
    const effectiveRole =
      req.user.role === "admin" ? req.user.role : req.user.activeRole;
    if (effectiveRole !== "seller" && req.user.role !== "admin") {
      return next(
        new ErrorResponse("Only sellers can access onboarding info", 403)
      );
    }

    const user = await User.findById(req.user.id);
    const sellerInfo = user.sellerInfo || {};

    // Calculate onboarding completion percentage
    const requiredFields = [
      "description",
      "aboutCoach",
      "sports",
      "expertise",
      "experiences",
      "minTrainingCost",
      "profileImage",
    ];

    const completedFields = requiredFields.filter((field) => {
      if (field === "experiences") {
        return sellerInfo.experiences && sellerInfo.experiences.length > 0;
      }
      if (field === "sports" || field === "expertise") {
        return sellerInfo[field] && sellerInfo[field].length > 0;
      }
      if (field === "profileImage") {
        // Check profile image in the main user object
        return user.profileImage && user.profileImage !== "" && user.profileImage !== "default-profile.jpg";
      }
      if (field === "aboutCoach") {
        // Check aboutCoach field and strip HTML tags for validation
        const cleanAboutCoach = sellerInfo.aboutCoach ? sellerInfo.aboutCoach.replace(/<[^>]*>/g, "").trim() : "";
        return cleanAboutCoach !== "";
      }
      return (
        sellerInfo[field] !== undefined &&
        sellerInfo[field] !== null &&
        sellerInfo[field] !== ""
      );
    });

    const completionPercentage = Math.round(
      (completedFields.length / requiredFields.length) * 100
    );

    res.status(200).json({
      success: true,
      data: {
        sellerInfo,
        onboardingStatus: {
          isComplete: sellerInfo.isOnboardingComplete || false,
          completionPercentage,
          completedFields: completedFields.length,
          totalFields: requiredFields.length,
          missingFields: requiredFields.filter(
            (field) => !completedFields.includes(field)
          ),
        },
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get all onboarded sellers for custom requests (shows sellers with completed onboarding regardless of current active role)
// @route   GET /api/users/onboarded-sellers
// @access  Private/Buyer
exports.getOnboardedSellers = async (req, res, next) => {
  try {
    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole = req.user.role === 'admin' ? req.user.role : req.user.activeRole;
    console.log(`[DEBUG] User requesting sellers: ${req.user.email}, Role: ${req.user.role}, ActiveRole: ${req.user.activeRole}, EffectiveRole: ${effectiveRole}`);

    if (effectiveRole !== 'buyer' && req.user.role !== 'admin') {
      return next(
        new ErrorResponse('Only buyers can access seller listings', 403)
      );
    }
    const sellers = await User.find({
      // Show users who have completed seller onboarding, regardless of current active role
      'sellerInfo.isOnboardingComplete': true,
      // Must be able to act as seller (either primary seller or dual-role user)
      $or: [
        { role: 'seller' }, // Primary sellers
        {
          role: 'buyer',
          // Dual-role users who have seller capabilities (have sellerInfo)
          'sellerInfo': { $exists: true }
        }
      ],
      // Only exclude if explicitly set to false
      isActive: { $ne: false },
      deletedAt: { $exists: false }
    })
    .select('firstName lastName email role activeRole sellerInfo.profileImage sellerInfo.description sellerInfo.aboutCoach sellerInfo.sports sellerInfo.expertise sellerInfo.minTrainingCost')
    .sort({ 'sellerInfo.minTrainingCost': 1 }); // Sort by minimum cost

    // Format the response
    const formattedSellers = sellers.map(seller => ({
      id: seller._id,
      name: `${seller.firstName} ${seller.lastName}`,
      email: seller.email,
      profileImage: seller.sellerInfo?.profileImage || null,
      description: seller.sellerInfo?.description || '',
      aboutCoach: seller.sellerInfo?.aboutCoach || '',
      sports: seller.sellerInfo?.sports || [],
      expertise: seller.sellerInfo?.expertise || [],
      minTrainingCost: seller.sellerInfo?.minTrainingCost || 0
    }));

    res.status(200).json({
      success: true,
      count: formattedSellers.length,
      data: formattedSellers
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Complete seller onboarding
// @route   POST /api/users/seller/complete-onboarding
// @access  Private/Seller
exports.completeSellerOnboarding = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if user has seller access (use effective role like the auth middleware)
    const effectiveRole =
      req.user.role === "admin" ? req.user.role : req.user.activeRole;
    if (effectiveRole !== "seller" && req.user.role !== "admin") {
      return next(
        new ErrorResponse("Only sellers can complete onboarding", 403)
      );
    }

    const {
      description,
      aboutCoach,
      experiences,
      minTrainingCost,
      socialLinks,
      sports,
      expertise,
      certifications,
      stripeConnectAccountId,
    } = req.body;

    const user = await User.findById(req.user.id);
    let sellerInfo = user.sellerInfo || {};
    let paymentInfo = user.paymentInfo || {};

    // Update sellerInfo with provided data
    if (description !== undefined) sellerInfo.description = description;
    if (aboutCoach !== undefined) sellerInfo.aboutCoach = aboutCoach;
    if (experiences !== undefined) sellerInfo.experiences = experiences;
    if (minTrainingCost !== undefined)
      sellerInfo.minTrainingCost = minTrainingCost;
    if (socialLinks !== undefined) sellerInfo.socialLinks = socialLinks;
    if (sports !== undefined) sellerInfo.sports = sports || [];
    if (expertise !== undefined) sellerInfo.expertise = expertise || [];
    if (certifications !== undefined)
      sellerInfo.certifications = certifications;

    // Update payment info with Stripe Connect account ID
    if (stripeConnectAccountId !== undefined) {
      paymentInfo.stripeConnectId = stripeConnectAccountId;
    }

    // Validate required fields for completion (only essential fields)
    const requiredFields = [
      { field: "description", message: "Description is required" },
      { field: "aboutCoach", message: "About the coach information is required" },
      { field: "experiences", message: "At least one experience is required" },
      {
        field: "minTrainingCost",
        message: "Minimum training cost is required",
      },
      {
        field: "profileImage",
        message: "Profile photo is required",
      },
      {
        field: "stripeConnectId",
        message: "Payment setup (Stripe Connect) is required",
      },
    ];

    const missingFields = [];

    for (const { field, message } of requiredFields) {
      if (field === "experiences") {
        if (!sellerInfo.experiences || sellerInfo.experiences.length === 0) {
          missingFields.push(message);
        }
      } else if (field === "stripeConnectId") {
        if (
          !paymentInfo.stripeConnectId ||
          paymentInfo.stripeConnectId === ""
        ) {
          missingFields.push(message);
        }
      } else if (field === "profileImage") {
        // Check profile image in the main user object
        if (!user.profileImage || user.profileImage === "" || user.profileImage === "default-profile.jpg") {
          missingFields.push(message);
        }
      } else if (field === "aboutCoach") {
        // Check aboutCoach field and strip HTML tags for validation
        const cleanAboutCoach = sellerInfo.aboutCoach ? sellerInfo.aboutCoach.replace(/<[^>]*>/g, "").trim() : "";
        if (!cleanAboutCoach) {
          missingFields.push(message);
        }
      } else {
        if (!sellerInfo[field] || sellerInfo[field] === "") {
          missingFields.push(message);
        }
      }
    }

    if (missingFields.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Cannot complete onboarding. Missing required fields.",
        missingFields,
      });
    }

    // Verify Stripe Connect account exists and is properly set up
    if (paymentInfo.stripeConnectId) {
      try {
        const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
        const connectAccount = await stripe.accounts.retrieve(
          paymentInfo.stripeConnectId
        );

        if (!connectAccount.details_submitted) {
          return res.status(400).json({
            success: false,
            message:
              "Stripe Connect onboarding is not complete. Please complete your payment setup.",
          });
        }
      } catch (stripeError) {
        console.error("Stripe Connect verification error:", stripeError);
        return res.status(400).json({
          success: false,
          message:
            "Invalid Stripe Connect account. Please complete your payment setup again.",
        });
      }
    }

    // Mark onboarding as complete and save all data
    sellerInfo.isOnboardingComplete = true;

    const updatedUser = await User.findByIdAndUpdate(
      req.user.id,
      {
        sellerInfo: sellerInfo,
        paymentInfo: paymentInfo,
      },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      message: "Seller onboarding completed successfully!",
      data: {
        sellerInfo: updatedUser.sellerInfo,
        paymentInfo: {
          stripeConnectId: updatedUser.paymentInfo?.stripeConnectId,
          hasPaymentSetup: !!updatedUser.paymentInfo?.stripeConnectId,
        },
      },
    });
  } catch (err) {
    next(err);
  }
};
