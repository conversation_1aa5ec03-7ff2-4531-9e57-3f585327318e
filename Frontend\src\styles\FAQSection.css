/* FAQ Section Styles */
.faq-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}



.faq-section .faq-container {
  position: relative;
  z-index: 1;
}

/* Header */
.faq-section .faq-header {
  text-align: center;
  margin-bottom: 1rem;
}

.faq-section .faq-title {
  font-size: var(--heading3);
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--secondary-color);
}

.faq-section .faq-subtitle {
  font-size: 1.125rem;
  color: var(--text-muted);
  margin: 0 auto;
  line-height: 1.6;
}

/* Controls */
.faq-section .faq-controls {
  margin-bottom: 2.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

.faq-section .faq-search {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.faq-section .search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.25rem;
  pointer-events: none;
}

.faq-section .search-input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 3rem;
  border: 2px solid var(--light-gray);
  border-radius: 50px;
  font-size: 1rem;
  background: var(--white);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.faq-section .search-input:focus {
  outline: none;
  border-color: var(--primary-color);

}

.faq-section .faq-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
}

.faq-section .category-btn {
  padding: 0.5rem 1.25rem;
  border: 2px solid var(--light-gray);
  border-radius: 25px;
  background: var(--white);
  color: var(--text-dark);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: capitalize;
}

.faq-section .category-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
}

.faq-section .category-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

/* FAQ List */
.faq-section .faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.faq-section .faq-item {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--light-gray);
}

.faq-section .faq-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.faq-section .faq-question {
  width: 100%;
  padding: 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.faq-section .faq-question:hover {
  background: rgba(var(--primary-rgb), 0.02);
}

.faq-section .faq-question.active {
  background: rgba(var(--primary-rgb), 0.05);
  border-bottom: 1px solid var(--light-gray);
}

.faq-section .faq-question-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.5;
  flex: 1;
}

.faq-section .faq-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  /* background: var(--primary-color); */
  color: var(--primary-color);
  font-size: 1.25rem;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.faq-section .faq-question:hover .faq-icon {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

.faq-section .faq-answer {
  overflow: hidden;
}

.faq-section .faq-answer-content {
  padding: 1rem;
  color: var(--text-muted);
  line-height: 1.7;
  font-size: 1rem;
}

/* Loading and Error States */
.faq-section .faq-loading,
.faq-section .faq-error {
  text-align: center;
  padding: 3rem 1rem;
}

.faq-section .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.faq-section .faq-error p {
  color: var(--error-color, #ef4444);
  font-size: 1.125rem;
}

.faq-section .no-faqs {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted);
  font-size: 1.125rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .faq-section .faq-title {
    font-size: var(--heading5);
  }

  .faq-section .faq-subtitle {
    font-size: 1rem;
  }

  .faq-section .faq-controls {
    gap: 1rem;
  }

  .faq-section .faq-search {
    max-width: 100%;
  }

  .faq-section .search-input {
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    font-size: 0.875rem;
  }

  .faq-section .search-icon {
    font-size: 1.125rem;
    left: 0.875rem;
  }

  .faq-section .category-btn {
    padding: 0.375rem 1rem;
    font-size: 0.8125rem;
  }

  .faq-section .faq-question {
    padding: 1.25rem;
    gap: 0.75rem;
  }

  .faq-section .faq-question-text {
    font-size: 1rem;
  }

  .faq-section .faq-icon {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 1.125rem;
  }

  .faq-section .faq-answer-content {
    padding: 0 1.25rem 1.25rem;
    font-size: 0.9375rem;
  }
}

@media (max-width: 480px) {
  .faq-section .faq-title {
    font-size: var(--heading5);
  }

  .faq-section .faq-question {
    padding: 1rem;
  }

  .faq-section .faq-question-text {
    font-size: 0.9375rem;
  }

  .faq-section .faq-answer-content {
    padding: 0 1rem 1rem;
    font-size: 0.875rem;
  }

  .faq-section .category-btn {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
  }
}
