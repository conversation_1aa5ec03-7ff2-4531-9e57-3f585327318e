.SectionWrapper {
  background-color: var(--white);
  border-radius: var(--border-radius);

  margin-bottom: 24px;
  width: 100%;
}
.SectionWrapper .bordrdiv {
  border-bottom: 1px solid #fddcdc;
  gap: 10px;
}

.SectionWrapper__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 10px;
}

.SectionWrapper .bordrdiv h2 {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: 8px 16px;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  position: relative;
  font-weight: 600;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  margin-bottom: 0px;
}

.SectionWrapper__action {
  display: flex;
  align-items: center;
  gap: 8px;
  
}

.SectionWrapper .SectionWrapper__title {
  font-size: var(--heading5);
  color: var(--secondary-color);

  font-weight: 600;
}

.SectionWrapper .SectionWrapper__content {
  width: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .SectionWrapper {
    margin-bottom: 16px;
  }

  .SectionWrapper .SectionWrapper__title {
    font-size: var(--heading6);
  }

 

  .SectionWrapper__action {
    margin-right: 0;
    width: fit-content;
    justify-content: flex-end;
  }
}
@media (max-width: 350px) {
  .SectionWrapper .bordrdiv h2 {

  padding: 10px 8px;

}
}