/* Seller Selection Component Styles */
.seller-selection {
  width: 100%;
}

.seller-selection .seller-selection-loading {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.seller-selection .seller-selection-empty {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.seller-selection .seller-selection-empty .empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  color: #d1d5db;
}

.seller-selection .seller-selection-empty h3 {
  margin: 0 0 8px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.seller-selection .seller-selection-empty p {
  margin: 0;
  line-height: 1.6;
}

/* Filters */
.seller-selection .seller-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.seller-selection .search-group {
  flex: 1;
  min-width: 200px;
}

.seller-selection .search-input {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.seller-selection .search-input:focus {
  outline: none;
  border-color: var(--dark-gray);

}

.seller-selection .filter-group {
  min-width: 150px;
}

.seller-selection .filter-select {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background-color: white;
  cursor: pointer;
}

.seller-selection .filter-select:focus {
  outline: none;
  border-color: var(--dark-gray);

}

.seller-selection .selection-actions {
  display: flex;
  gap: 8px;
}

.seller-selection .select-all-btn {
  padding: 10px 16px;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.seller-selection .select-all-btn:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.seller-selection .select-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Selection Summary */
.seller-selection .selection-summary {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 20px;
}

.seller-selection .selection-count {
  font-weight: 500;
  color: #1d4ed8;
  font-size: 0.875rem;
}

/* Sellers List */
.seller-selection .sellers-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.seller-selection .sellers-list::-webkit-scrollbar {
  width: 6px;
}

.seller-selection .sellers-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.seller-selection .sellers-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.seller-selection .sellers-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.seller-selection .no-results {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

/* Seller Card */
.seller-selection .seller-card {
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
}

.seller-selection .seller-card:hover {
  border-color: var(--dark-gray);

}

.seller-selection .seller-card.selected {
  border-color: var(--dark-gray);
  background-color: #eff6ff;
 
}

.seller-selection .seller-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.seller-selection .seller-info {
  display: flex;
  gap: 12px;
  flex: 1;
}

.seller-selection .seller-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
}

.seller-selection .seller-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.seller-selection .seller-avatar svg {
  font-size: 1.5rem;
  color: #6b7280;
}

.seller-selection .seller-details {
  flex: 1;
}

.seller-selection .seller-name {
  margin: 0 0 4px 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.seller-selection .seller-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.875rem;
  color: #6b7280;
}

.seller-selection .min-cost {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.seller-selection .selection-indicator {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.seller-selection .seller-card.selected .selection-indicator {
  opacity: 1;
}

.seller-selection .seller-card-body {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.seller-selection .seller-description {
  margin: 0;
  color: #6b7280;
  line-height: 1.5;
  font-size: 0.875rem;
}

.seller-selection .seller-sports,
.seller-selection .seller-expertise {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.seller-selection .seller-sports strong,
.seller-selection .seller-expertise strong {
  font-size: 0.75rem;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.seller-selection .sports-tags,
.seller-selection .expertise-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.seller-selection .sport-tag,
.seller-selection .expertise-tag {
  background-color: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.seller-selection .sport-tag.more,
.seller-selection .expertise-tag.more {
  background-color: #e5e7eb;
  color: #6b7280;
}

/* Results Info */
.seller-selection .results-info {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.seller-selection .results-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
  .seller-selection .seller-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .seller-selection .search-group,
  .seller-selection .filter-group {
    min-width: auto;
  }
  
  .seller-selection .selection-actions {
    justify-content: center;
  }
  
  .seller-selection .seller-card-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .seller-selection .selection-indicator {
    align-self: flex-end;
  }
  
  .seller-selection .sellers-list {
    max-height: 300px;
  }
}
