import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  selectMyRequests,
  selectLoading,
  selectErrors,
  fetchBuyerRequests,
  clearError,
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import {
  TableRowSkeleton,
} from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaSync, FaPlus } from "react-icons/fa";
import { FiEye, FiClock, FiCheck, FiX, FiDollarSign } from "react-icons/fi";
import Table from "../../components/common/Table";
import CreateCustomRequestModal from "../../components/buyer/CreateCustomRequestModal";
import "../../styles/BuyerRequests.css";
import "../../styles/Table.css";
import { MdRequestPage } from "react-icons/md";
import { formatStandardDate } from "../../utils/dateValidation";
import { FaCreditCard } from "react-icons/fa";
import { toast } from "react-toastify";
import api from "../../services/api";

const BuyerRequests = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const requests = useSelector(selectMyRequests);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Local state
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Fetch requests on component mount
  useEffect(() => {
    dispatch(fetchBuyerRequests());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError("requests"));
    dispatch(fetchBuyerRequests());
  };

  const handleCreateRequest = () => {
    setShowCreateModal(true);
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
  };

  const handleRequestCreated = () => {
    // Refresh requests after creating a new one
    dispatch(fetchBuyerRequests());
    setShowCreateModal(false);
  };

  const handleViewDetails = (request) => {
    navigate(`/buyer/account/request-details/${request._id}`);
  };

  const handlePayment = async (request) => {
    try {
      console.log('Payment Details:', request.paymentDetails);

      // Check if there's an order to pay for
      let orderId = null;

      if (request.paymentDetails?.initialOrderId && !request.paymentDetails?.initialPaymentCompleted) {
        orderId = request.paymentDetails.initialOrderId;
        console.log('Using initial order ID:', orderId);
      } else if (request.paymentDetails?.finalOrderId && !request.paymentDetails?.finalPaymentCompleted) {
        orderId = request.paymentDetails.finalOrderId;
        console.log('Using final order ID:', orderId);
      }

      if (orderId) {
        console.log('Navigating to checkout with order ID:', orderId);

        // First, verify the order actually exists
        try {
          await api.get(`/orders/${orderId}`);
          // Order exists, proceed to checkout
          navigate(`/checkout/${orderId}`);
        } catch (orderError) {
          if (orderError.response?.status === 404) {
            console.log('Order not found, attempting to recreate...');
            toast.info('Fixing payment order... Please wait.');

            // Try to create the missing order
            try {
              const response = await api.post(`/requests/${request._id}/create-payment-order`);
              if (response.data.success) {
                const { orderId: newOrderId } = response.data.data;
                toast.success('Payment order fixed! Proceeding to checkout...');
                navigate(`/checkout/${newOrderId}`);
              } else {
                throw new Error(response.data.message || 'Failed to create payment order');
              }
            } catch (createError) {
              console.error('Error creating payment order:', createError);
              toast.error(createError.response?.data?.message || 'Unable to fix payment order. Please contact support.');
            }
          } else {
            throw orderError;
          }
        }
      } else {
        console.log('No order ID found. Request status:', request.status);
        console.log('Payment details:', request.paymentDetails);

        // If request is accepted but no order exists, try to create one
        if (request.status === 'Accepted' && !request.paymentDetails?.initialOrderId) {
          toast.info('Creating payment order... Please wait.');
          try {
            // Call the backend to create the missing order
            const response = await api.post(`/requests/${request._id}/create-payment-order`);
            if (response.data.success) {
              const { orderId } = response.data.data;
              toast.success('Payment order created successfully!');
              navigate(`/checkout/${orderId}`);
            } else {
              throw new Error(response.data.message || 'Failed to create payment order');
            }
          } catch (createOrderError) {
            console.error('Error creating payment order:', createOrderError);
            toast.error(createOrderError.response?.data?.message || 'Unable to create payment order. Please contact support.');
          }
        } else {
          toast.error('No payment is required at this time. Please wait for the seller to accept your request.');
        }
      }
    } catch (error) {
      console.error('Error navigating to payment:', error);
      toast.error('Unable to process payment at this time');
    }
  };

  // Ensure requests is always an array
  const requestsArray = Array.isArray(requests) ? requests : [];

  // Filter requests based on status and search term
  const filteredRequests = requestsArray.filter(request => {
    const matchesFilter = filter === 'all' || request.status?.toLowerCase() === filter.toLowerCase();
    const matchesSearch = !searchTerm ||
      request.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesFilter && matchesSearch;
  });

  const getStatusBadge = (status) => {
    const statusConfig = {
      'Pending': { color: 'orange', icon: <FiClock /> },
      'Accepted': { color: 'green', icon: <FiCheck /> },
      'Rejected': { color: 'red', icon: <FiX /> },
      'In Progress': { color: 'blue', icon: <FiClock /> },
      'Content Submitted': { color: 'purple', icon: <FiCheck /> },
      'Completed': { color: 'green', icon: <FiCheck /> }
    };

    const config = statusConfig[status] || { color: 'gray', icon: <FiClock /> };

    return (
      <span className={`status-badge status-${config.color}`}>
        {config.icon}
        {status}
      </span>
    );
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    { key: "request", label: "Request Details", className: "request" },
    { key: "seller", label: "Seller", className: "seller" },
    { key: "budget", label: "Budget/Price", className: "budget" },
    { key: "date", label: "Date", className: "date" },
    { key: "status", label: "Status", className: "status" },
    { key: "action", label: "Actions", className: "action" },
  ];

  const renderRow = (request, index) => [
    <td key="no" className="no">
      {index + 1}
    </td>,
    <td key="request" className="request">
      <div className="request-details">
        <div className="request-title">{request.title || 'Untitled Request'}</div>
        <div className="request-meta">
          <span className="content-type">{request.contentType}</span>
          <span className="sport">{request.sport}</span>
        </div>

      </div>
    </td>,
    <td key="seller" className="seller">
      <div className="seller-info">
        <div className="seller-name">
          {request.seller?.firstName} {request.seller?.lastName}
        </div>
        <div className="seller-email">{request.seller?.email}</div>
      </div>
    </td>,
    <td key="budget" className="budget">
      <div className="budget-info">
        <div className="budget-amount">${request.budget}</div>
        {request.sellerResponse?.price && (
          <div className="seller-price">Seller: ${request.sellerResponse.price}</div>
        )}
      </div>
    </td>,
    <td key="date" className="date">
      {formatStandardDate(request.createdAt)}
    </td>,
    <td key="status" className="status">
      {getStatusBadge(request.status)}
    </td>,
    <td key="action" className="action">
      <div className="action-buttons">
        <button
          className="action-btn view-btn"
          onClick={() => handleViewDetails(request)}
          title="View Details"
        >
          <FiEye />
        </button>
        {request.status === 'Accepted' &&
          !request.paymentDetails?.initialPaymentCompleted && (
            <button
              className="action-btn payment-btn"
              onClick={() => handlePayment(request)}
              title="Make Payment"
            >
              <FaCreditCard /> Payment
            </button>
          )}
        {request.remainingPaymentRequested &&
          !request.paymentDetails?.finalPaymentCompleted && (
            <button
              className="action-btn payment-btn"
              onClick={() => handlePayment(request)}
              title="Pay Remaining Amount"
            >
              <FaCreditCard /> Pay Remaining
            </button>
          )}
      </div>
    </td>,
  ];

  return (
    <div className="BuyerRequests">
      <SectionWrapper
        icon={<MdRequestPage className="BuyerSidebar__icon" />}
        title="My Requests"
        action={
          <div className="section-actions">
            <button
              className="create-request-btn"
              onClick={handleCreateRequest}
              title="Make a Custom Request"
            >
              <FaPlus /> New Request
            </button>
            {errors.requests && (
              <button
                className="retry-btn"
                onClick={handleRetry}
                title="Retry loading requests"
              >
                <FaSync />
              </button>
            )}
          </div>
        }
      >
        {/* Filters */}
        <div className="requests-filters">
          <div className="filter-group">
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="accepted">Accepted</option>
              <option value="in progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>

          <div className="search-group">
            <input
              type="text"
              placeholder="Search requests..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
        </div>

        {/* Stats */}
        <div className="requests-stats">
          <div className="stat-item">
            <span className="stat-count">{requestsArray.length}</span>
            <span className="stat-label">Total Requests</span>
          </div>
          <div className="stat-item">
            <span className="stat-count">
              {requestsArray.filter(r => r.status === 'Pending').length}
            </span>
            <span className="stat-label">Pending</span>
          </div>
          <div className="stat-item">
            <span className="stat-count">
              {requestsArray.filter(r => r.status === 'Completed').length}
            </span>
            <span className="stat-label">Completed</span>
          </div>
        </div>

        {/* Content */}
        {errors.requests ? (
          <ErrorDisplay
            error={errors.requests}
            onRetry={handleRetry}
            title="Failed to load requests"
          />
        ) : loading.requests ? (
          <div className="loading-container">
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
          </div>
        ) : filteredRequests.length > 0 ? (

          <Table
            columns={columns}
            data={filteredRequests}
            renderRow={renderRow}
            className="table"
            emptyMessage="No requests match your current filters."
          />

        ) : requestsArray.length > 0 ? (
          <div className="BuyerRequests__empty">
            <h3>No matching requests</h3>
            <p>
              No requests match your current search or filter criteria.
              Try adjusting your filters or search terms.
            </p>
          </div>
        ) : (
          <div className="BuyerRequests__empty">
            <MdRequestPage className="empty-icon" />
            <h3>No custom requests yet</h3>
            <p>
              You haven't made any custom content requests yet. Click the "Make a Custom Request" button above to get started.
            </p>
            <button
              className="create-request-btn-large"
              onClick={handleCreateRequest}
            >
              <FaPlus /> Make Your First Custom Request
            </button>
          </div>
        )}
      </SectionWrapper>

      {/* Create Custom Request Modal */}
      <CreateCustomRequestModal
        isOpen={showCreateModal}
        onClose={handleCloseModal}
        onRequestCreated={handleRequestCreated}
      />
    </div>
  );
};

export default BuyerRequests;
