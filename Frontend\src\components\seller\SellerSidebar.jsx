import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/sellerDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/SellerSidebar.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdLocalOffer } from "react-icons/md";
import { MdVideoLibrary } from "react-icons/md";
import { FaCreditCard } from "react-icons/fa";
import { MdPayment } from "react-icons/md";
import { IoLogOut } from "react-icons/io5";

const SellerSidebar = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);

  // Handle tab click
  const handleTabClick = (tab) => {
    dispatch(setActiveTab(tab));

    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/seller/dashboard");
        break;
      case "my-sports-strategies":
        navigate("/seller/my-sports-strategies");
        break;
      case "requests":
        navigate("/seller/requests");
        break;
      case "bids":
        navigate("/seller/bids");
        break;
      case "offers":
        navigate("/seller/offers");
        break;
      case "cards":
        navigate("/seller/cards");
        break;
      case "payment-settings":
        navigate("/seller/payment-settings");
        break;
      case "profile":
        navigate("/seller/profile");
        break;
      default:
        navigate("/seller/dashboard");
    }
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  return (
    <div className="SellerSidebar">
      <div className="SellerSidebar__container">
        <ul className="SellerSidebar__menu">
          <li
            className={`SellerSidebar__item ${activeTab === "dashboard" ? "active" : ""
              }`}
            onClick={() => handleTabClick("dashboard")}
          >
            <MdDashboard className="SellerSidebar__icon" />
            <span>Dashboard</span>
          </li>

          <li
            className={`SellerSidebar__item ${activeTab === "my-sports-strategies" ? "active" : ""
              }`}
            onClick={() => handleTabClick("my-sports-strategies")}
          >
            <MdVideoLibrary className="SellerSidebar__icon" />
            <span>My Sports Strategies</span>
          </li>

          <li
            className={`SellerSidebar__item ${activeTab === "requests" ? "active" : ""
              }`}
            onClick={() => handleTabClick("requests")}
          >
            <MdRequestPage className="SellerSidebar__icon" />
            <span>My Requests</span>
          </li>

          <li
            className={`SellerSidebar__item ${activeTab === "bids" ? "active" : ""
              }`}
            onClick={() => handleTabClick("bids")}
          >
            <FaGavel className="SellerSidebar__icon" />
            <span>Bids</span>
          </li>

          <li
            className={`SellerSidebar__item ${activeTab === "offers" ? "active" : ""
              }`}
            onClick={() => handleTabClick("offers")}
          >
            <MdLocalOffer className="SellerSidebar__icon" />
            <span>Offers</span>
          </li>

          {/* <li
            className={`SellerSidebar__item ${
              activeTab === "cards" ? "active" : ""
            }`}
            onClick={() => handleTabClick("cards")}
          >
            <FaCreditCard className="SellerSidebar__icon" />
            <span>My Cards</span>
          </li> */}

          <li
            className={`SellerSidebar__item ${activeTab === "payment-settings" ? "active" : ""
              }`}
            onClick={() => handleTabClick("payment-settings")}
          >
            <MdPayment className="SellerSidebar__icon" />
            <span>Payment Settings</span>
          </li>

          <li
            className={`SellerSidebar__item ${activeTab === "profile" ? "active" : ""
              }`}
            onClick={() => handleTabClick("profile")}
          >
            <FaUser className="SellerSidebar__icon" />
            <span>My Profile</span>
          </li>

          <li
            className="SellerSidebar__item SellerSidebar__logout"
            onClick={handleLogout}
          >
            <IoLogOut className="SellerSidebar__icon" />
            <span>Logout</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default SellerSidebar;
