import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/sellerDashboardSlice";
import SellerSidebar from "./SellerSidebar";
import "../../styles/SellerLayout.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser, FaGavel, FaCreditCard } from "react-icons/fa";
import { MdRequestPage, MdVideoLibrary, MdPayment } from "react-icons/md";
import { AiOutlineArrowLeft } from "react-icons/ai";
import { MdLocalOffer } from "react-icons/md";

const SellerLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const activeTab = useSelector(selectActiveTab);

  // Map routes to tabs
  const routeToTabMap = {
    "/seller/dashboard": "dashboard",
    "/seller/my-sports-strategies": "my-sports-strategies",
    "/seller/my-sports-strategies/add": "my-sports-strategies",
    "/seller/requests": "requests",
    "/seller/bids": "bids",
    "/seller/offers": "offers",
    "/seller/cards": "cards",
    "/seller/payment-settings": "payment-settings",
    "/seller/profile": "profile",
  };

  // Handle dynamic routes
  const getDynamicTab = () => {
    if (location.pathname.startsWith("/seller/strategy-details/")) {
      return "my-sports-strategies";
    }
    if (location.pathname.startsWith("/seller/request-details/")) {
      return "requests";
    }
    if (location.pathname.startsWith("/seller/bid-details/")) {
      return "bids";
    }
    if (location.pathname.startsWith("/seller/offer-details/")) {
      return "offers";
    }
    return null;
  };

  // Boolean page matchers
  const isAddStrategyPage =
    location.pathname === "/seller/my-sports-strategies/add";
  const isEditDetailsPage = /^\/seller\/strategy-details\/[^/]+\/edit$/.test(
    location.pathname
  );
  const isStrategyDetailsPage =
    location.pathname.startsWith("/seller/strategy-details/") &&
    !isEditDetailsPage;
  const isRequestDetailsPage = location.pathname.startsWith(
    "/seller/request-details/"
  );
  const isBidDetailsPage = location.pathname.startsWith("/seller/bid-details/");
  const isOfferDetailsPage = location.pathname.startsWith(
    "/seller/offer-details/"
  );

  // Header configuration
  const headerConfig = {
    dashboard: {
      title: "Dashboard",
      icon: <MdDashboard />,
    },
    "my-sports-strategies": {
      title: "My Sports Strategies",
      icon: <MdVideoLibrary />,
    },
    requests: {
      title: "My Requests",
      icon: <MdRequestPage />,
    },
    bids: {
      title: "Bids",
      icon: <FaGavel />,
    },
    offers: {
      title: "Offers",
      icon: <MdLocalOffer />,
    },
    cards: {
      title: "My Cards",
      icon: <FaCreditCard />,
    },
    "payment-settings": {
      title: "Payment Settings",
      icon: <MdPayment />,
    },
    profile: {
      title: "My Profile",
      icon: <FaUser />,
    },
  };

  // Get current header info
  const currentHeader = headerConfig[activeTab] || headerConfig["dashboard"];

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname] || getDynamicTab();
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);

  return (
    <div className="SellerLayout">
      <div className="container max-container">
        <div className="sidebar">
          <SellerSidebar />
        </div>

        <div className="outerdiv">
          {!isAddStrategyPage &&
            !isStrategyDetailsPage &&
            !isEditDetailsPage &&
            !isRequestDetailsPage &&
            !isBidDetailsPage &&
            !isOfferDetailsPage && (
              <div className="bordrdiv mb-30">
                <h2 className="SellerLayout__title">
                  {currentHeader.icon}
                  {currentHeader.title}
                </h2>
                {activeTab === "my-sports-strategies" && (
                  <button
                    className="add-strategy-btn btn-outline"
                    onClick={() => navigate("/seller/my-sports-strategies/add")}
                  >
                    Add New Strategy
                  </button>
                )}
              </div>
            )}

          {isAddStrategyPage && (
            <div className="bordrdiv mb-30">
              <div className="AddStrategy__header-container">
                <button
                  className="AddStrategy__back-btn"
                  onClick={() => navigate("/seller/my-sports-strategies")}
                >
                  <AiOutlineArrowLeft className="AddStrategy__back-icon" />
                  Back
                </button>
                <h3 className="newcssforh3">Add New Strategy</h3>
              </div>
            </div>
          )}

          {isEditDetailsPage && (
            <div className="bordrdiv mb-30">
              <div className="AddStrategy__header-container">
                <button
                  className="AddStrategy__back-btn"
                  onClick={() => navigate("/seller/my-sports-strategies")}
                >
                  <AiOutlineArrowLeft className="AddStrategy__back-icon" />
                  Back
                </button>
                <h3 className="newcssforh3">Edit Strategy</h3>
              </div>
            </div>
          )}

          {isStrategyDetailsPage && (
            <div className="bordrdiv mb-30">
              <div className="AddStrategy__header-container">
                <button
                  className="AddStrategy__back-btn"
                  onClick={() => navigate("/seller/my-sports-strategies")}
                >
                  <AiOutlineArrowLeft className="AddStrategy__back-icon" />
                  Back
                </button>
                <h3 className="newcssforh3">Details Page</h3>
              </div>
            </div>
          )}

          {isRequestDetailsPage && (
            <div className="bordrdiv mb-30">
              <div className="AddStrategy__header-container">
                <button
                  className="AddStrategy__back-btn"
                  onClick={() => navigate("/seller/requests")}
                >
                  <AiOutlineArrowLeft className="AddStrategy__back-icon" />
                  Back
                </button>
                <h3 className="newcssforh3">Request Details</h3>
              </div>
            </div>
          )}

          {isBidDetailsPage && (
            <div className="bordrdiv mb-30">
              <div className="AddStrategy__header-container">
                <button
                  className="AddStrategy__back-btn"
                  onClick={() => navigate("/seller/bids")}
                >
                  <AiOutlineArrowLeft className="AddStrategy__back-icon" />
                  Back
                </button>
                <h3 className="newcssforh3">Bid Details</h3>
              </div>
            </div>
          )}

          {isOfferDetailsPage && (
            <div className="bordrdiv mb-30">
              <div className="AddStrategy__header-container">
                <button
                  className="AddStrategy__back-btn"
                  onClick={() => navigate("/seller/offers")}
                >
                  <AiOutlineArrowLeft className="AddStrategy__back-icon" />
                  Back
                </button>
                <h3 className="newcssforh3">Offer Details</h3>
              </div>
            </div>
          )}

          <div className="contentArea">
            <div className="SellerLayout__content">{children}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerLayout;
